import { isEmptyValues } from '@uni/utils/src/utils';
import React, { useEffect, useState } from 'react';

export const useContainerResize = (elementId?: string, formKey?: string) => {
  const [containerSize, setContainerSize] = useState<number | undefined>();

  // 优化后的容器尺寸监听实现
  useEffect(() => {
    const getTargetElement = (): HTMLElement | null => {
      if (isEmptyValues(elementId)) {
        return document.getElementById('tanstack-table-container');
      }
      return document.querySelector(
        `#${elementId} #tanstack-table-container`,
      ) as HTMLElement;
    };

    const getParentElement = (): Element | null => {
      return formKey
        ? document.querySelector(
            `${formKey} div[class~='uni-drag-edit-table-container']`,
          )
        : null;
    };

    const updateContainerSize = () => {
      const targetElement = getTargetElement();
      const size = targetElement?.offsetWidth;
      setContainerSize(size);
    };

    // 初始化设置
    updateContainerSize();

    // 状态管理
    let resizeObserver: ResizeObserver | null = null;
    let mutationObserver: MutationObserver | null = null;
    let intervalId: NodeJS.Timeout | null = null;
    let isCleanedUp = false;

    const cleanup = () => {
      if (isCleanedUp) return;
      isCleanedUp = true;

      if (resizeObserver) {
        resizeObserver.disconnect();
        resizeObserver = null;
      }
      if (mutationObserver) {
        mutationObserver.disconnect();
        mutationObserver = null;
      }
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
    };

    const setupResizeObserver = (element: Element) => {
      cleanup();

      resizeObserver = new ResizeObserver((entries) => {
        // 使用 requestAnimationFrame 确保在下一帧更新
        requestAnimationFrame(() => {
          if (!isCleanedUp) {
            updateContainerSize();
          }
        });
      });

      resizeObserver.observe(element);
    };

    const setupMutationObserver = () => {
      if (!formKey) return;

      mutationObserver = new MutationObserver((mutations) => {
        let shouldCheck = false;

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' || mutation.type === 'attributes') {
            shouldCheck = true;
          }
        });

        if (shouldCheck && !isCleanedUp) {
          const parentElement = getParentElement();
          if (parentElement) {
            setupResizeObserver(parentElement);
          }
        }
      });

      // 观察整个文档的变化
      mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'id'],
      });
    };

    const setupFallbackTimer = () => {
      intervalId = setInterval(() => {
        if (isCleanedUp) return;

        updateContainerSize();

        // 尝试找到父元素并切换到 ResizeObserver
        const parentElement = getParentElement();
        if (parentElement) {
          setupResizeObserver(parentElement);
        }
      }, 1000);
    };

    // 主要逻辑
    const parentElement = getParentElement();

    if (parentElement) {
      // 找到父元素，直接使用 ResizeObserver
      setupResizeObserver(parentElement);
    } else if (formKey) {
      // 没找到但有 formKey，使用 MutationObserver + 备用定时器
      setupMutationObserver();
      setupFallbackTimer();
    } else {
      // 没有 formKey，只使用定时器
      setupFallbackTimer();
    }

    // 清理函数
    return cleanup;
  }, [elementId, formKey]);

  return containerSize;
};
